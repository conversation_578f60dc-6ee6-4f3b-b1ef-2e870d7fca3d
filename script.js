var typed = new Typed('#element', {
    strings: ['Full-Stack Developer.', 'Software Developer Intern.', '&amp; Web Designer.'],
    typeSpeed: 50,
    loop:true,
  });
console.log("welcome! to My portfolio");

var sidemenu = document.getElementById("sidebar");
function openmenu(){
  sidemenu.style.right = "0";
}
function closemenu(){
  sidemenu.style.right = "-200px";
}

// Tab functionality
function opentab(tabname, element) {
  var tablinks = document.getElementsByClassName("tab-link");
  var tabcontents = document.getElementsByClassName("tab-contents");

  for (var i = 0; i < tablinks.length; i++) {
    tablinks[i].classList.remove("active-link");
  }

  for (var i = 0; i < tabcontents.length; i++) {
    tabcontents[i].classList.remove("active-tab");
  }

  if (element) {
    element.classList.add("active-link");
  }
  document.getElementById(tabname.substring(1)).classList.add("active-tab");
}