<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio</title>
    <link rel="icon" href="" type="image/icon type">
    <link rel="stylesheet" href="./style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <script src="https://kit.fontawesome.com/44a6e55a64.js" crossorigin="anonymous"></script>     
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

</head>
<body style="font-family: poppins;">
    <div class="header" style=" margin-block: -45px;">
    <div class="dropdown">
        <nav class="backdrop-blur-md">
            <h2 class="font-semibold" ><span style="color: rgb(69, 106, 228);">A</span>ditya's Portfolio</h2>
            <ul id="sidebar">
                <li><a href="">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#portfolio">Projects</a></li>
                <li><a href="#contact ">Contact</a></li>
             
             <i class=" fas fa-regular fa-circle-xmark " onclick="closemenu()"></i>
            </ul>
            <i class=" fas fa-solid fa-bars"  onclick="openmenu()"></i> 
            
        </nav>
    </div>
    <div class="first">
        <div class="left">
            <h2 class="font-bold">Hello, I'm <span style="color: rgb(44, 83, 210);">Aditya</span><br>
                <span id="element"></span>
                
            </h2>
            <p style="color: rgb(201, 200, 200);">As a BCA student in 6th semester and Software Developer Intern at OMX Digital PVT LTD, I bring expertise in full-stack development with PHP (Laravel), React.js, and modern web technologies. I specialize in crafting responsive and user-centric web applications.</p><br>
                <button class="btn1"><a href="https://www.linkedin.com/in/aditya-jaiswal-359453245/" target="_blank">Hire Me </a>   </button>                
                <button class="btn2"><span class="spn2"><a href="./img/My cv.pdf" download>
                    Download CV
                  </a></span></button>                



        </div>
        <div class="right">
            <img src="./img/a1.png" alt="">
            
        </div>




    </div>
    <script src="https://unpkg.com/typed.js@2.1.0/dist/typed.umd.js"></script>
    <script src="script.js"> </script>
</div>

</div>

<div id="about">
    <div class="container">
    <div class="about-cont">
        <div class="row">
            <div class="aboutcol-1">
                <img src="./img/IMG-20240822-WA0029.jpg" alt="">
            </div>
            <div class="aboutcol-2">
                <h1 class="font-bold">About Me</h1>
                <p>Hi there! I'm Aditya Jaiswal, a passionate Full-Stack Developer currently pursuing BCA from Inspiria Knowledge Campus (CGPA: 7.82/10). I'm currently working as a Software Developer at OMX Digital PVT LTD, where I collaborate with cross-functional teams to build and maintain web applications using PHP (Laravel) and React.js.</p>
                     <div class="tab-titles">
                        <p class="tab-link active-link" onclick="opentab('#Skills', this)">Skills</p>
                        <p class="tab-link" onclick="opentab('#Experience', this)">Experience</p>

                    </div>
                    <div class="tab-contents active-tab" id="Skills">
                        <h4><span style="color: #515be9;">Programming Languages</span></h4>
                        <ul>
                            <li>&#8226; PHP</li>
                            <li>&#8226; JavaScript</li>
                             <li>&#8226; TypeScript</li>
                            <li>&#8226; HTML5 & CSS3</li>
                            <li>&#8226; SQL</li>
                        </ul>
                        <h4><span style="color: #515be9;">Frameworks & Libraries</span></h4>
                        <ul>
                            <li>&#8226; Laravel</li>
                            <li>&#8226; React.js</li>
                            <li>&#8226; Next.js</li>
                            <li>&#8226; Node.js</li>
                            <li>&#8226; Bootstrap</li>
                            <li>&#8226; Tailwind CSS</li>
                        </ul>
                        <h4><span style="color: #515be9;">Tools & Platforms</span></h4>
                        <ul>
                            <li>&#8226; Git & GitHub</li>
                            <li>&#8226; Postman</li>
                            <li>&#8226; XAMPP</li>
                            <li>&#8226; VS Code</li>

                            <li>&#8226; npm</li>
                        </ul>
                    </div>
                    <div class="tab-contents" id="Experience">
                        <h4><span style="color: #515be9;">Software Developer Intern</span></h4>
                        <p style="color: #888; margin-bottom: 10px;">OMX Digital PVT LTD | Feb 2025 - Present</p>
                        <ul>
                            <li>&#8226; Collaborated with cross-functional development team to build and maintain web applications using PHP (Laravel) and React.js</li>
                            <li>&#8226; Enhanced application performance and user experience through optimized code and responsive design</li>
                            <li>&#8226; Used Git for version control and participated in regular code reviews to ensure code quality and best practices</li>
                            <li>&#8226; Participated in full software development lifecycle including requirement analysis, coding, debugging, testing, and deployment</li>
                        </ul>
                    </div>
                    <div id="Education"><p class="tab-link active-link"> Education </p><br>
                        </div>
                    <div class="edu" >
                        
                        
                        <ul>
                            <li><i class='fas fa-graduation-cap'></i> Bachelor of Computer Application (BCA) - Inspiria Knowledge Campus
                                </li>
                                <li style=" margin-left:25px">Aug 2022 - June 2025 | CGPA: 7.82/10</li>
                                <li style=" margin-left:25px; color: #888; font-size: 14px;">Coursework: Data Structures & Algorithms, DBMS, Operating Systems, Computer Networks, Web Development, OOP, Software Engineering, Cloud Computing</li><br>
                            <li><i class='fas fa-graduation-cap'></i> Higher Secondary (H.S) - Hindi Madhyamik Vidyalaya
                                </li>
                                <li style=" margin-left:25px">2022 | Percentage: 78.2%</li><br>

                        </ul>
                    </div>
                    <div id="Certificate"><p class="tab-link active-link">Certifications</p></div><br>
                    <div class="edu">
                        
                        <ul>
                            <li><i class="fa-solid fa-certificate"></i> Certificate of Full Stack Mastery <a href="https://www.udemy.com/certificate/UC-80ecbaf9-0d2e-45ae-aeab-0f78375e8966/" target="_blank">View</a><br>
                                <i class="fa-solid fa-certificate"></i> Certificate of WebDevelopment internship <a href="https://www.linkedin.com/in/aditya-jaiswal-web/details/certifications/1731725577174/single-media-viewer/?profileId=ACoAADzBqzUBDSZDUy4mA8cKeVw4dtqdDfQYI_o" target="_blank">View</a><br>
                            </li>

                        </ul>
                    </div>
                    <div id="Activities"><p class="tab-link active-link">Extra Curricular Activities</p></div><br>
                    <div class="edu">
                        <ul>
                            <li><i class="fa-solid fa-users"></i> Active member of Computing Club - Participating in tech discussions, workshops, and events</li><br>
                            <li><i class="fa-solid fa-trophy"></i> Event Organization - Organized inter-college gaming competitions including Free Fire mobile game tournament, enhancing event planning and team coordination skills</li>
                        </ul>
                    </div>

            </div>
        </div>
    </div>
</div>

</div>
<div id="sevices">
    <div class="container">
        <h1 class="sub-title">My Services</h1>
        <div class="services-list">
            <div class="card">
                <i class="fa-solid fa-code"></i>
            <h2>Web Design</h2>
            <p>The project involved creating a visually striking and user-friendly design that highlights the photographer's portfolio. Using HTML, CSS, and
                 JavaScript, I developed a responsive website that looks great on all devices. </p>
        <a href="">learn more
        </a>
    </div>
    <div class="card">
        <i class="fa-solid fa-code"></i>
        <h2>UX/UI Design</h2>
        <p>Comming soon </p>
    <a href="">learn more
    </a>
</div>
<div class="card" >
    <i class="fa-solid fa-code"></i>    
    <h2> Website Maintenance and Optimization</h2>
    <p>Regular updates, bug fixes, and security patches are essential for keeping a website running smoothly.  
         </p>
<a href="">learn more
</a>
    </div> 
            
        </div>
    </div>
 
</div>
<div id="portfolio">
    <div class="container">
        <h1 class="sub-title">My Work </h1>
        <div class="work-li">
           <div class="work">
            <img src="/img/work12.jpg" alt="">
            <div class="layer">
                <h3>Netflix Homepage Clone</h3>
                <p>Responsive front-end clone of Netflix homepage using HTML5, CSS3, and Bootstrap. Focused on clean UI/UX, mobile responsiveness, and pixel-perfect design replication.</p>
                <a href="https://netflix-home-page-clone--tau.vercel.app/"><i class="fa-solid fa-arrow-up-right-from-square"></i></a>
            </div>
      
           </div>  
           <div class="work">
            <img src="/img/calculator.png" alt="">
            <div class="layer">
                <h3> Web Calculator </h3>
                <p>Creating a web calculator involves a combination of HTML, CSS, and JavaScript. HTML structures the calculator's layout, defining buttons, displays, and other elements.

                </p>
                <a href="https://calculator-ten-xi-75.vercel.app/ " target="_blank"><i class="fa-solid fa-arrow-up-right-from-square"></i></a>
            </div>
      
           </div>
           <div class="work">
            <img src="/img/work3.png" alt="">
            <div class="layer">
                <h3>social media app</h3>
                <p>the app connect nothing </p>
                <a href="https://code-soft-apjrpd79v-aditya-jaiswals-projects-4036915c.vercel.app"   target="_blank"><i class="fa-solid fa-arrow-up-right-from-square"></i></a>
            </div> 
      
           </div>
        </div>
        <a href="#" class="btn">See More!</a>
    </div>
</div>
<div id="contact">
    <div class="container">
        <div class="row">
            <div class="contact-left">
                <h1 class="sub-title">Contact Me</h1>
                <p>Let's Connect
                    I'm currently looking for new oppoutunities, my inbox is always open, Whether you have a question or just want to say hi, I'll try my best to get back to you!  </p>
                <p><i class="fa-solid fa-envelope"></i><EMAIL></p>
                <p><i class="fa-solid fa-phone"></i>+91-8327693057</p>
                <p><i class="fa-solid fa-map-marker-alt"></i>Siliguri, India</p>
                <div class="social-icon">

                    <a href="https://github.com/adityajaiswal69"><i class="fa-brands fa-github"></i></a>
                    
                    <a href="https://www.linkedin.com/in/aditya-jaiswal-359453245/"><i class="fa-brands fa-linkedin"></i></a>
                   
                    
                </div><br>
                <button class="btn2 btnd" ><span class="spn2"><a href="./img/My cv.pdf" download>
                    Download CV
                  </a></span></button>    
                

            </div>
            <div class="contact-right">
                
<div class="card">
  <span class="title">Leave a Comment</span>
  <form class="form" name="submit-to-google-sheet">
    <div class="group">
    <input placeholder="" type="text" required="">
    <label for="name">Name</label>
    </div>
<div class="group">
    <input placeholder="" type="email" id="email" name="email" required="">
    <label for="email">Email</label>
    </div>
<div class="group"> 
    <textarea placeholder="" id="comment" name="comment" rows="5" required=""></textarea>
    <label for="comment">Comment</label>
</div>
    <button type="submit">Submit</button>
  </form>
</div>

               
            </div>
        </div>
    </div>
    <div class="container">
    <hr class="line" color="#252525">
    
    <div class="copyright">
        Aditya <p>All Right Reserved</p>
    </div>
    <hr class="line">
</div>

</div>

<script>
    
</script>
</body>
</html>
