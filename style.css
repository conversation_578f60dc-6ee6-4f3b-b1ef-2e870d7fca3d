/* Modern CSS Reset */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Custom Properties for Modern Design */
:root {
    /* Colors */
    --primary-bg: #0a0a0a;
    --secondary-bg: #111111;
    --card-bg: rgba(255, 255, 255, 0.05);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --accent-primary: #00d4ff;
    --accent-secondary: #ff6b6b;
    --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #ff6b6b 100%);
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666666;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    --spacing-3xl: 6rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);

    /* Typography */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
    --font-size-5xl: 3rem;
}

/* Modern Body Styles */
body {
    font-family: var(--font-family);
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* Modern Background with Animated Gradient */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-gradient);
    border-radius: 10px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #00b8e6 0%, #e55555 100%);
}
/* Modern Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Modern Navigation */
nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-xl);
    z-index: 1000;
    transition: all 0.3s ease;
}

nav.scrolled {
    background: rgba(10, 10, 10, 0.95);
    box-shadow: var(--shadow-lg);
}

nav h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

nav ul {
    display: flex;
    gap: var(--spacing-xl);
    list-style: none;
}

nav ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-base);
    position: relative;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
}

nav ul li a:hover {
    color: var(--text-primary);
    background: var(--glass-bg);
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-gradient);
    transform: translateX(-50%);
    transition: width 0.3s ease;
}

nav ul li a:hover::after {
    width: 100%;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--spacing-xs);
}

.menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}
/* Modern Hero Section */
.hero {
    min-height: 100vh;
    margin-top: 100px;
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    position: relative;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    width: 100%;
}

.hero-text {
    z-index: 2;
}

.hero-text h1 {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text .subtitle {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.hero-text .typed-text {
    font-size: var(--font-size-xl);
    color: var(--accent-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-lg);
}

.hero-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
}
/* Modern Button Group */
.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Modern Button Styles */
.btn-modern {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--accent-gradient);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}
/* Hero Image Section */
.hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: var(--accent-gradient);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.1;
    animation: pulse 3s ease-in-out infinite;
    z-index: 1;
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.1; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.2; }
}
/* Modern Section Styles */
.section {
    padding: var(--spacing-2xl) 0;
    position: relative;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern About Section */
#about {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

#about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: var(--spacing-3xl);
    align-items: center;
    justify-items: center;
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
}

.about-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    justify-self: center;
}

.about-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.about-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 90%;
    height: 90%;
    background: var(--accent-gradient);
    border-radius: var(--radius-xl);
    transform: translate(-50%, -50%) rotate(-5deg);
    opacity: 0.1;
    z-index: 1;
    transition: all 0.4s ease;
}

.about-image:hover::before {
    transform: translate(-50%, -50%) rotate(-2deg) scale(1.05);
    opacity: 0.2;
}

.about-image:hover img {
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    width: 100%;
    justify-self: start;
}

.about-intro {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.about-intro::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-gradient);
}

.about-intro p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.8;
    margin: 0;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Modern Tab System */
.tab-container {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.tab-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-gradient);
}

.tab-titles {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-link {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
    position: relative;
    border: none;
    background: none;
}

.tab-link:hover {
    color: var(--text-primary);
    background: var(--glass-bg);
}

.tab-link.active-link {
    color: var(--accent-primary);
    background: rgba(0, 212, 255, 0.1);
}

.tab-link.active-link::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-gradient);
    border-radius: 2px;
}

/* Tab Content */
.tab-contents {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-contents.active-tab {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-contents h4 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.tab-contents h4 span {
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tab-contents ul {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.tab-contents ul li {
    list-style: none;
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-left: 3px solid var(--accent-primary);
}

.tab-contents ul li:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

/* Modern Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.service-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-primary);
}

.service-card i {
    font-size: var(--font-size-4xl);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
    display: block;
}

.service-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.service-card a {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-card a:hover {
    color: var(--accent-secondary);
}

/* Modern Portfolio Section */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.project-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--accent-gradient);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-sm);
    font-weight: 600;
    transition: all 0.3s ease;
}

.project-link:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.project-content {
    padding: var(--spacing-lg);
}

.project-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.project-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.tech-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-primary);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Modern Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.contact-info {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
}

.contact-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.contact-info p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.contact-details {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.contact-details li {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    color: var(--text-secondary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-details li:last-child {
    border-bottom: none;
}

.contact-details i {
    color: var(--accent-primary);
    font-size: var(--font-size-lg);
    width: 20px;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--glass-bg);
    color: var(--text-primary);
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
    background: var(--accent-gradient);
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

/* Modern Contact Form */
.contact-form {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-group label {
    display: block;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    transition: all 0.3s ease;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--accent-gradient);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

/* Modern Footer */
.footer {
    background: var(--secondary-bg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xl) 0;
    text-align: center;
}

.footer p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.footer .highlight {
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.75rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 3rem;
    }

    .container {
        padding: 0 var(--spacing-md);
    }

    nav {
        padding: 0 var(--spacing-md);
    }

    nav ul {
        display: none;
    }

    .menu-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .hero-text {
        order: 2;
    }

    .hero-image {
        order: 1;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
        justify-items: center;
        max-width: 600px;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .about-image img {
        max-width: 300px;
    }

    .tab-titles {
        flex-wrap: wrap;
        justify-content: center;
    }

    .tab-link {
        flex: 1;
        min-width: 120px;
    }

    .services-grid,
    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .hero-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: var(--font-size-4xl);
    }

    .btn-modern {
        width: 100%;
        justify-content: center;
    }

    .tab-titles {
        flex-direction: column;
    }

    .tab-link {
        width: 100%;
    }

    .about-stats {
        grid-template-columns: 1fr 1fr;
    }

    .stat-item {
        padding: var(--spacing-sm);
    }

    .stat-number {
        font-size: var(--font-size-xl);
    }
}

/* Modern Animations and Effects */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Loading animation for images */
.hero-image img,
.about-image img,
.project-image img {
    opacity: 0;
    animation: fadeIn 1s ease forwards;
}

/* Hover effects for interactive elements */
.service-card,
.project-card,
.tab-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom selection colors */
::selection {
    background: var(--accent-primary);
    color: var(--primary-bg);
}

::-moz-selection {
    background: var(--accent-primary);
    color: var(--primary-bg);
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}
.right{
height: auto;
  margin-block-start: 20vh;    
}

.btn1 {
    padding: 15px 25px;
    border: unset;
    border-radius: 20px;
    color: rgb(68, 109, 247);
    z-index: 1;
    background: transparent;
    position: relative;
    font-weight: 1000;
    font-size: 17px;
    cursor: pointer;
    
    
    transition: all 250ms;
   }
   .btn1 a{
    text-decoration: none;
    color: #fff;
   }
   
.btn1::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    border-radius: 20px;
    background-color: rgba(16, 72, 255, 0.651);
    z-index: -1;
    
    transition: all 250ms
   }
   
.btn1:hover {
    color: #e8e8e8;
   }
   
.btn1:hover::before {
    width: 100%;
   }

.btn1{
    border: none;
    border-radius: 20px;
    width:fit-content ;
    padding-top: .75rem;
    padding-bottom: .75rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    color: #fff;
    background-color:rgb(10, 34, 112);
    margin-left: 20px;
    margin-right: 20px;
}

.btn2 {
    position: relative;
    display: inline-block;
    padding: 15px 20px;
    border: 2px solid #fefefe;
    border-radius: 20px;
    text-transform: uppercase;
    color: #fefefe;
    text-decoration: none;
    font-weight: 600;
    font-size: 15px;
    transition: 0.3s;
  }
  .btn2 a{
    text-decoration: none;
    color: #ffffff
  }
  .btn2::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 4px);
    height: calc(100% - -2px);
    background-color:  rgb(7, 15, 40);
    transition: 0.3s ease-out;
    transform: scaleY(1);
  }
  
  .btn2::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 5px);
    height: calc(100% - 50px);
   
    transition: 0.3s ease-out;
    transform: scaleY(1);
  }
  
  .btn2:hover::before {
    transform: translateY(-25px);
    
    height: 0;
  }
  
  .btn2:hover::after {
    transform: scaleX(0);
    transition-delay: 0.15s;
  }
  
  .btn2:hover {
    border: 2px solid #fefefe;
    
  }
  
  .btn2 span {
    position: relative;
    z-index: 1;
  }
  
  button {
    text-decoration: none;
    border: none;
    background-color: transparent;
  }
  #about{
    
    padding: 80px 0;
    height: auto;
    width: 100%;
    box-shadow: 0px -0px 200px rgba(128, 128, 128, 0.404);
    margin-top: 20px;
  }
  .row{
    
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
      
  }  
  .aboutcol-1 img{
    width: 300px;
    border-radius: 50px;
    height: 500px;
    object-fit: cover;

  }

.aboutcol-1 {
  flex-basis: 35%;
  
    
}
.aboutcol-2{
  flex-basis: 60%;
}
.tab-titles{
  display: flex;
  margin: 20px 0 20px;
}
.tab-link{
  margin-right: 50px;
  font-size: 18px;
  font-weight: 500;
  cursor:  pointer  ;
  position: relative;
  width: 40%;
   
}
.tab-link::after{
  position: absolute;
  content: '';
  width: 0;
  height: 3px;
  background: rgb(60, 60, 252);
  left: 0;
  bottom: -8px;
  transition: 0.5s;
   
}
.tab-link.active-link::after{
  width: 100%;
}

.tab-contents ul {
  display: flex;
 
  
}
.edu ul li{
   list-style: none;

}
.edu ul li a{
  text-decoration: none;
  color: #126bf0;
}

.tab-contents ul li{
list-style: none;
  margin-right: 20px  ;
  margin-bottom: 10px;

}

.tab-contents{
 display: none; 
}
.tab-contents.active-tab{
  display: block;
}
#sevices{ 
  height: auto;
  
  background:  rgb(7, 15, 40);
}
.services-list{
  display: grid;
  grid-template-columns: repeat(auto-fit,minmax(250px,1fr));
  grid-gap:40px ;
  margin-top: 50px;
  
  


}
.services-list .card:hover{
  background: #1b018b;  
  transform: translateY(-20px);

}




.services-list .card {
  background: #0d246367;
  padding: 40px;
  font-size: 13px;
  font-weight: 300;
  border-radius: 15px; 
  transition: background 0.5s, transform 0.5s ;
  position: relative;
  
  
}
.services-list div  i{
  font-size: 50px;
  margin-bottom: 20px;

}
.services-list div h2{
  font-weight: 500;
  
}

.services-list div a{
  text-decoration: none;
  color: white;
  margin-top: 20px;
  font-size: 12px;
  display: inline-block;

}

.work-li{
  display: grid;
  grid-template-columns: repeat(auto-fit,minmax(250px,1fr));
  grid-gap:40px;
  margin-top:50px;
}
.work{
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}
.work img{
  width: 100%;
  image-resolution: auto-fit;
  object-fit:cover;
  border-radius: 10px;
  display: block;
  transition: 0.5s;

}
.layer{
  width: 100%;
  height: 0%;
  background: linear-gradient(rgba(0,0,0,0.6),#126bf0);
  border-radius: 10px;
  position: absolute;
  left: 0;
  bottom: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 40px;
  font-size: 14px;
  transition: 0.3s;
  
}

.layer h3{
  font-weight: 500;
  margin-bottom:20px ;

}
.layer a{
  margin-top: 20px;
  color: #126bf0; 
  text-decoration: none;
  font-size: 18px;
  line-height: 60px;
  background: #fff;
  width: 60px;
  border-radius: 50%;
  height: 60px;
  text-align: center;
}
.work:hover img{
  transform: scale(1.1);
}
.work:hover .layer{
  height: 100%; 
}

.btn{
  display: block;
  margin: 50px auto;
  width: fit-content;
  border:  1px solid #126bf0;
  padding: 14px 50px;
  text-decoration: none;
  border-radius: 6px;
  color: #fff;
  transition: 0.5s;


}
.btn:hover{ 
  background: rgb(43, 128, 226);
  
}

.contact-left{
  flex-basis: 35%
  
}

.contact-right{
  flex-basis: 65%
  
}

.contact-left p{
  margin-top: 30px; 
  
}
.contact-left p i{
  color: #0165fc;
  margin-right: 13px;
  font-size: 25px;
}
.social-icon{
  margin-top: 30px;
}
.social-icon a{
  text-decoration: none;
  margin-right: 15px;
  color:#b7b8bb ;
  display: inline-block;
  transition: transform 0.5s;
  font-size: 30px;
}
.social-icon a:hover{
  color: #126bf0;
  transform: translateY(-15px)
}

.contact-right form{
  
  width: 100%;

}

.card {
  background-color: #ffffff00;
  border-radius: 10px;
  padding: 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}

.form {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
}

.group {
  position: relative;
}

.form .group label {
  font-size: 14px;
  color: rgb(99, 102, 102);
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: rgb(7, 15, 40);
  transition: all .3s ease;
}

.form .group input,
.form .group textarea {
  padding: 10px;
  border-radius: 5px;
  border: 1px solid rgba(243, 241, 241, 0.77);
  margin-bottom: 45px;
  outline: 0;
  width: 100%;
  background-color: transparent;
}

.form .group input:placeholder-shown+ label, .form .group textarea:placeholder-shown +label {
  top: 10px;
  background-color: transparent;
}

.form .group input:focus,
.form .group textarea:focus {
  border-color: #3366cc;
}

.form .group input:focus+ label, .form .group textarea:focus +label {
  top: -10px;
  left: 10px;
  background-color: rgb(7, 15, 40);
  color: #3366cc;
  font-weight: 600;
  font-size: 14px;
}

.form .group textarea {
  resize: none;
  height: 100px;
}

.form button {
  background-color: #3366cc;
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 10px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form button:hover {
  background-color: #27408b;
}



form input,form textarea{
  color: #fff;
  font-size: 18px;
  border-radius: 6px;
  
}
  
.copyright{
  padding: 15px ;
  height: 15vh;
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 200;
  justify-content: space-between;
}


/* Base styles (Desktop) */
body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
}
nav .fas{
 display: none;
}
/* Mobile styles */
@media only screen and (max-width: 768px) {
  
  .header nav ul {
      display: flex;
      flex-direction: column;
      align-items: center;
  }
  nav ul {
    background: #0165fc;
    position: fixed;
    top: 0;
    right: -200px;
    width: 200px;
    height: 100vh;
    padding-top: 50px;
    transition: right 0.7s;
  }

 
  nav .fas{
    display: block;
  }
  .header nav ul li {
      margin: 10px 0;
      display: block;
  }
  nav ul .fas{
    position: absolute;
    top: 25px;
    left: 25px;
    cursor: pointer;
  }
  .first {
      display: flex;
      flex-direction: column;
      
      padding: 50px 10px 0px 10px;
  }

  .left{
    padding: 0px 20px;
    text-align: center;
  }
  .left p{
    width: 100%;
  }
  .right{
    align-items: center;
  }
  .first .left, .first .right {
      width: 100%;
      
  }

  .first .right img {
      width: 100%;
      margin: 20px auto;
  }

  .aboutcol-1, .aboutcol-2 {
      width: 100%;
      text-align: center;
  }

  .tab-contents ul li{
    list-style: none;
      margin-right: 10px  ;
      margin-bottom: 10px;
    
    }
  .tab-titles {
    display: flex;
    justify-content: center;
    margin: 20px 0 20px;
}
  .tab-link{
    margin: 0;
  }

.row{
  justify-content: space-around;
}
  .services-list {
      flex-direction: column;
      align-items: center;
  }

  .card {
      width: 90%;
      margin: 20px 0;
  }
.container{
  padding: 20px;
}
  .work-li {
      display: flex;
      flex-direction: column;
      align-items: center;
  }

  .work {
      width: 100%;
      margin: 20px 0;
      
  }
  

  .contact-left, .contact-right {
      width: 100%;
      text-align: center;
  }

  .contact-right .card {
      width: 100%;
      margin: 20px auto;
  }
  button {
    width: 100%;
    padding: 15px;
    font-size: 16px;
}

h2, h1, p {
    font-size: 1.2em;
    line-height: 1.5;
}
}
